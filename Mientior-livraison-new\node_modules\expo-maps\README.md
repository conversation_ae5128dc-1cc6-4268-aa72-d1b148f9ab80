> 🚨 Expo Maps is currently in alpha and subject to breaking changes. It is not available in the Expo Go app.

<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/maps/">
    <img
      src="../../.github/resources/expo-maps.svg"
      alt="expo-maps"
      height="64" />
  </a>
</p>

Provides a Map component that uses Google Maps on Android and Apple Maps on iOS. Requires a minimum deployment target of iOS 18.0.

# API documentation

- [Documentation for the main branch](https://docs.expo.dev/versions/unversioned/sdk/maps/)

# Installation

```shell
npx expo install expo-maps
```

# Contributing

Contributions are not encouraged at this time.
