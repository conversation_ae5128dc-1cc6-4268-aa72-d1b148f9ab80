{"name": "expo-maps", "version": "0.10.0", "description": "Provides a Map component that uses Google Maps on Android and Apple Maps on iOS.", "main": "src/index.ts", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "homepage": "https://docs.expo.dev/versions/latest/sdk/ui/", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-ui"}, "keywords": ["react-native", "expo", "UI components"], "author": "650 Industries, Inc.", "license": "MIT", "dependencies": {}, "devDependencies": {"@types/react": "~19.0.10", "expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "49c9d53cf0a9fc8179d1c8f5268beadd141f70ca"}