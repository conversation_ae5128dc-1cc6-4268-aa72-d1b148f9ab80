# 🔧 Correction du problème Google Maps

## ❌ Problème identifié
Votre clé API Google Maps est **restreinte** et bloque les requêtes depuis votre IP actuelle (`*************`).

## ✅ Solutions

### Solution 1 : Modifier les restrictions (Rapide)

1. **Ouvrez Google Cloud Console** : https://console.cloud.google.com/
2. **Allez dans "APIs et services" > "Identifiants"**
3. **Cliquez sur votre clé API** (AIzaSyBgNf...)
4. **Dans "Restrictions d'application"** :
   - Sélectionnez **"Aucune restriction"** 
   - OU ajoutez votre IP : `*************`
5. **Cliquez sur "Enregistrer"**

### Solution 2 : Créer une nouvelle clé API

1. **Dans Google Cloud Console > "Identifiants"**
2. **Cliquez sur "+ CRÉER DES IDENTIFIANTS" > "Clé API"**
3. **Copiez la nouvelle clé**
4. **Remplacez dans votre fichier `.env`** :
   ```env
   GOOGLE_MAPS_API_KEY=VOTRE_NOUVELLE_CLE_ICI
   ```

## 🔄 Test après correction

Après avoir modifié les restrictions, testez avec :
```bash
node test-google-maps.js
```

Vous devriez voir :
```
✅ Clé API Google Maps fonctionne correctement !
```

## 🚀 Redémarrage de l'application

Après correction :
```bash
npx expo start --clear
```

## 🔒 Sécurité pour la production

Pour la production, configurez des restrictions appropriées :
- **Applications Android** : `com.livraisonafrique.mobile`
- **Applications iOS** : `com.livraisonafrique.mobile`
- **APIs autorisées** : Maps SDK for Android, Maps SDK for iOS, Geocoding API

## 📱 APIs à activer

Assurez-vous que ces APIs sont activées dans Google Cloud :
- ✅ Maps SDK for Android
- ✅ Maps SDK for iOS  
- ✅ Maps JavaScript API
- ✅ Geocoding API
- ✅ Places API (optionnel)
