#!/usr/bin/env node

/**
 * Test rapide pour vérifier si Google Maps fonctionne
 */

const https = require('https');
const fs = require('fs');

// Lire la clé API
function getApiKey() {
  try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const match = envContent.match(/GOOGLE_MAPS_API_KEY=(.+)/);
    return match ? match[1].trim() : null;
  } catch (error) {
    return null;
  }
}

// Test simple
async function quickTest() {
  const apiKey = getApiKey();
  
  if (!apiKey) {
    console.log('❌ Clé API non trouvée');
    return;
  }
  
  console.log('🔍 Test rapide Google Maps...');
  
  const url = `https://maps.googleapis.com/maps/api/geocode/json?address=Dakar&key=${apiKey}`;
  
  https.get(url, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.status === 'OK') {
          console.log('✅ Google Maps fonctionne !');
          console.log('📍 Résultats:', response.results.length);
        } else {
          console.log('❌ Erreur:', response.status);
          if (response.error_message) {
            console.log('💬', response.error_message);
          }
        }
      } catch (error) {
        console.log('❌ Erreur de parsing:', error.message);
      }
    });
  }).on('error', (error) => {
    console.log('❌ Erreur réseau:', error.message);
  });
}

quickTest();
