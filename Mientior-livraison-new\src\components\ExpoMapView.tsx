import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { MapView, Marker } from 'expo-maps';

const { height: screenHeight } = Dimensions.get('window');

interface ExpoMapViewProps {
  style?: any;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showUserLocation?: boolean;
  userLocation?: {
    latitude: number;
    longitude: number;
  } | null;
}

export const ExpoMapView: React.FC<ExpoMapViewProps> = ({ 
  style, 
  region = {
    latitude: 14.6928, // Dakar, Sénégal
    longitude: -17.4467,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  showUserLocation = false,
  userLocation = null
}) => {
  console.log('🗺️ ExpoMapView rendering with region:', region);
  console.log('👤 User location:', userLocation);

  return (
    <View style={[styles.container, style]}>
      <MapView
        style={styles.map}
        initialRegion={region}
        provider="google"
        onMapReady={() => {
          console.log('✅ ExpoMapView: Map ready');
        }}
        onMapLoaded={() => {
          console.log('✅ ExpoMapView: Map loaded');
        }}
        showsUserLocation={false}
        showsMyLocationButton={false}
        scrollEnabled={false}
        zoomEnabled={false}
        rotateEnabled={false}
        pitchEnabled={false}
      >
        {/* Marqueur pour la position utilisateur */}
        {showUserLocation && userLocation && (
          <Marker
            coordinate={userLocation}
            title="Votre position"
            description="Vous êtes ici"
          />
        )}
        
        {/* Marqueur par défaut pour Dakar */}
        {!userLocation && (
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Dakar, Sénégal"
            description="Localisation par défaut"
          />
        )}
      </MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  map: {
    width: '100%',
    height: '100%',
  },
});
