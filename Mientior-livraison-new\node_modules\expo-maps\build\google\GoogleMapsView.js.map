{"version": 3, "file": "GoogleMapsView.js", "sourceRoot": "", "sources": ["../../src/google/GoogleMapsView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAQtD,IAAI,UAAU,GAAoD,IAAI,CAAC;AAEvE,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;IAC9B,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,cAAc,CAAI,WAA+B;IACxD,OAAO,KAAK,CAAC,WAAW;IACtB,sGAAsG;IACtG,CAAC,KAAU,EAAE,EAAE;QACb,WAAW,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,EACD,CAAC,WAAW,CAAC,CACd,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,CAC5C,CACE,EACE,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACV,aAAa,EACb,eAAe,EACf,aAAa,EACb,cAAc,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,OAAO,EACP,QAAQ,EACR,GAAG,KAAK,EACT,EACD,GAAG,EACH,EAAE;IACF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAqB,IAAI,CAAC,CAAC;IACzD,KAAK,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACpC,iBAAiB,CAAC,MAAgC;YAChD,SAAS,CAAC,OAAO,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;KACF,CAAC,CAAC,CAAC;IAEJ,MAAM,iBAAiB,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QAC/C,WAAW,EAAE,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAClB,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IACpD,MAAM,oBAAoB,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IACpD,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAC1D,MAAM,kBAAkB,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;IACxD,MAAM,qBAAqB,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;IAC9D,MAAM,oBAAoB,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAE1D,MAAM,eAAe,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpD,GAAG,QAAQ;QACX,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS;KACjD,CAAC,CAAC,CAAC;IAEJ,MAAM,aAAa,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC9C,GAAG,MAAM;QACT,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,SAAS;QAC9C,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,SAAS;KACvD,CAAC,CAAC,CAAC;IAEJ,MAAM,aAAa,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC9C,GAAG,MAAM;QACT,mBAAmB;QACnB,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,yBAAyB;KAC7C,CAAC,CAAC,CAAC;IAEJ,MAAM,cAAc,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,GAAG,OAAO;QACV,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS;QAC/C,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS;KACxD,CAAC,CAAC,CAAC;IAEJ,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CACL,CAAC,UAAU,CACT,IAAI,KAAK,CAAC,CACV,GAAG,CAAC,CAAC,SAAS,CAAC,CACf,OAAO,CAAC,CAAC,aAAa,CAAC,CACvB,SAAS,CAAC,CAAC,eAAe,CAAC,CAC3B,QAAQ,CAAC,CAAC,cAAc,CAAC,CACzB,OAAO,CAAC,CAAC,aAAa,CAAC,CACvB,WAAW,CAAC,CAAC,iBAAiB,CAAC,CAC/B,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAC7B,cAAc,CAAC,CAAC,oBAAoB,CAAC,CACrC,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAC7B,aAAa,CAAC,CAAC,mBAAmB,CAAC,CACnC,YAAY,CAAC,CAAC,kBAAkB,CAAC,CACjC,eAAe,CAAC,CAAC,qBAAqB,CAAC,CACvC,cAAc,CAAC,CAAC,oBAAoB,CAAC,CACrC,aAAa,CAAC,CAAC,mBAAmB,CAAC,EACnC,CACH,CAAC;AACJ,CAAC,CACF,CAAC", "sourcesContent": ["import { requireNativeView } from 'expo';\nimport * as React from 'react';\nimport { Platform, processColor } from 'react-native';\n\nimport type {\n  GoogleMapsViewProps,\n  GoogleMapsViewType,\n  SetCameraPositionConfig,\n} from './GoogleMaps.types';\n\nlet NativeView: React.ComponentType<GoogleMapsViewProps> | null = null;\n\nif (Platform.OS === 'android') {\n  NativeView = requireNativeView('ExpoGoogleMaps');\n}\n\nfunction useNativeEvent<T>(userHandler?: (data: T) => void) {\n  return React.useCallback(\n    // TODO(@kitten): We unwrap a native payload here, but this isn't reflected in NativeView's prop types\n    (event: any) => {\n      userHandler?.(event.nativeEvent);\n    },\n    [userHandler]\n  );\n}\n\n/**\n * @platform android\n */\nexport const GoogleMapsView = React.forwardRef<GoogleMapsViewType, GoogleMapsViewProps>(\n  (\n    {\n      onMapLoaded,\n      onMapClick,\n      onMapLongClick,\n      onPOIClick,\n      onMarkerClick,\n      onPolylineClick,\n      onCircleClick,\n      onPolygonClick,\n      onCameraMove,\n      markers,\n      polylines,\n      circles,\n      polygons,\n      ...props\n    },\n    ref\n  ) => {\n    const nativeRef = React.useRef<GoogleMapsViewType>(null);\n    React.useImperativeHandle(ref, () => ({\n      setCameraPosition(config?: SetCameraPositionConfig) {\n        nativeRef.current?.setCameraPosition(config);\n      },\n    }));\n\n    const onNativeMapLoaded = React.useCallback(() => {\n      onMapLoaded?.();\n    }, [onMapLoaded]);\n    const onNativeMapClick = useNativeEvent(onMapClick);\n    const onNativeMapLongClick = useNativeEvent(onMapLongClick);\n    const onNativePOIClick = useNativeEvent(onPOIClick);\n    const onNativeMarkerClick = useNativeEvent(onMarkerClick);\n    const onNativeCameraMove = useNativeEvent(onCameraMove);\n    const onNativePolylineClick = useNativeEvent(onPolylineClick);\n    const onNativePolygonClick = useNativeEvent(onPolygonClick);\n    const onNativeCircleClick = useNativeEvent(onCircleClick);\n\n    const parsedPolylines = polylines?.map((polyline) => ({\n      ...polyline,\n      color: processColor(polyline.color) ?? undefined,\n    }));\n\n    const parsedCircles = circles?.map((circle) => ({\n      ...circle,\n      color: processColor(circle.color) ?? undefined,\n      lineColor: processColor(circle.lineColor) ?? undefined,\n    }));\n\n    const parsedMarkers = markers?.map((marker) => ({\n      ...marker,\n      // @ts-expect-error\n      icon: marker.icon?.__expo_shared_object_id__,\n    }));\n\n    const parsedPolygons = polygons?.map((polygon) => ({\n      ...polygon,\n      color: processColor(polygon.color) ?? undefined,\n      lineColor: processColor(polygon.lineColor) ?? undefined,\n    }));\n\n    if (!NativeView) {\n      return null;\n    }\n    return (\n      <NativeView\n        {...props}\n        ref={nativeRef}\n        markers={parsedMarkers}\n        polylines={parsedPolylines}\n        polygons={parsedPolygons}\n        circles={parsedCircles}\n        onMapLoaded={onNativeMapLoaded}\n        onMapClick={onNativeMapClick}\n        onMapLongClick={onNativeMapLongClick}\n        onPOIClick={onNativePOIClick}\n        onMarkerClick={onNativeMarkerClick}\n        onCameraMove={onNativeCameraMove}\n        onPolylineClick={onNativePolylineClick}\n        onPolygonClick={onNativePolygonClick}\n        onCircleClick={onNativeCircleClick}\n      />\n    );\n  }\n);\n"]}