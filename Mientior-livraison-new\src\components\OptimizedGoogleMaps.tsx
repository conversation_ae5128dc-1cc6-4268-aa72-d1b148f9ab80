import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';

interface OptimizedGoogleMapsProps {
  style?: any;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showUserLocation?: boolean;
  userLocation?: {
    latitude: number;
    longitude: number;
  } | null;
  scrollEnabled?: boolean;
  zoomEnabled?: boolean;
  rotateEnabled?: boolean;
  pitchEnabled?: boolean;
  onMapReady?: () => void;
  onMapLoaded?: () => void;
}

export const OptimizedGoogleMaps: React.FC<OptimizedGoogleMapsProps> = ({ 
  style, 
  region = {
    latitude: 14.6928, // Dakar, Sénégal
    longitude: -17.4467,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  showUserLocation = false,
  userLocation = null,
  scrollEnabled = false,
  zoomEnabled = false,
  rotateEnabled = false,
  pitchEnabled = false,
  onMapReady,
  onMapLoaded
}) => {
  const [mapReady, setMapReady] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);

  useEffect(() => {
    console.log('🗺️ OptimizedGoogleMaps: Initializing Google Maps');
    console.log('📍 Region:', region);
    console.log('👤 User location:', userLocation);
    console.log('📱 Platform:', Platform.OS);
  }, [region, userLocation]);

  const handleMapReady = () => {
    console.log('✅ Google Maps: Map ready');
    setMapReady(true);
    onMapReady?.();
  };

  const handleMapLoaded = () => {
    console.log('✅ Google Maps: Map loaded successfully');
    setMapLoaded(true);
    onMapLoaded?.();
  };

  return (
    <View style={[styles.container, style]}>
      <MapView
        provider={PROVIDER_GOOGLE} // Force Google Maps
        style={styles.map}
        initialRegion={region}
        region={region}
        onMapReady={handleMapReady}
        onMapLoaded={handleMapLoaded}
        showsUserLocation={false} // Géré manuellement pour plus de contrôle
        showsMyLocationButton={false}
        scrollEnabled={scrollEnabled}
        zoomEnabled={zoomEnabled}
        rotateEnabled={rotateEnabled}
        pitchEnabled={pitchEnabled}
        loadingEnabled={true}
        loadingIndicatorColor="#0DCAA8"
        mapType="standard" // Google Maps standard
        // Optimisations Google Maps
        showsBuildings={true}
        showsTraffic={false}
        showsIndoors={true}
        showsCompass={false}
        showsScale={false}
        // Style Google Maps personnalisé
        customMapStyle={[
          {
            featureType: "all",
            stylers: [
              { saturation: -10 }, // Légèrement désaturé
              { lightness: 5 }     // Légèrement plus clair
            ]
          },
          {
            featureType: "poi",
            elementType: "labels",
            stylers: [
              { visibility: "simplified" } // Simplifier les POI
            ]
          }
        ]}
      >
        {/* Marqueur pour la position utilisateur avec style personnalisé */}
        {showUserLocation && userLocation && (
          <Marker
            coordinate={userLocation}
            title="Votre position"
            description="Vous êtes ici"
            pinColor="#0DCAA8" // Couleur de votre thème
          />
        )}
        
        {/* Marqueur par défaut pour Dakar avec style personnalisé */}
        {!userLocation && (
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Dakar, Sénégal"
            description="Localisation par défaut"
            pinColor="#FF6B6B" // Couleur différente pour le distinguer
          />
        )}
      </MapView>
      
      {/* Indicateur de statut en overlay */}
      {(!mapReady || !mapLoaded) && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingIndicator} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0DCAA8',
    opacity: 0.8,
  },
});
