{"version": 3, "file": "shared.types.js", "sourceRoot": "", "sources": ["../src/shared.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PermissionResponse } from 'expo-modules-core';\n\nexport type Coordinates = {\n  /**\n   * The latitude of the coordinate.\n   */\n  latitude?: number;\n\n  /**\n   * The longitude of the coordinate.\n   */\n  longitude?: number;\n};\n\nexport type CameraPosition = {\n  /**\n   * The middle point of the camera.\n   */\n  coordinates?: Coordinates;\n\n  /**\n   * The zoom level of the camera.\n   * For some view sizes, lower zoom levels might not be available.\n   */\n  zoom?: number;\n};\n\n/**\n * @hidden\n */\nexport type MapsModule = {\n  /**\n   * Asks the user to grant permissions for location.\n   * @return A promise that fulfills with an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  requestPermissionsAsync(): Promise<PermissionResponse>;\n  /**\n   * Checks user's permissions for accessing location.\n   * @return A promise that fulfills with an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  getPermissionsAsync(): Promise<PermissionResponse>;\n};\n"]}