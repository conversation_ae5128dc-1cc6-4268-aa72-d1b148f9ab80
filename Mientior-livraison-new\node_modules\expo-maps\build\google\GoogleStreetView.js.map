{"version": 3, "file": "GoogleStreetView.js", "sourceRoot": "", "sources": ["../../src/google/GoogleStreetView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAIxC,IAAI,UAAU,GAAsD,IAAI,CAAC;AAEzE,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;IAC9B,UAAU,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAA4B;IAC3D,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAG,CAAC;AACnC,CAAC", "sourcesContent": ["import { requireNativeView } from 'expo';\nimport * as React from 'react';\nimport { Platform } from 'react-native';\n\nimport { GoogleStreetViewProps } from './GoogleMaps.types';\n\nlet NativeView: React.ComponentType<GoogleStreetViewProps> | null = null;\n\nif (Platform.OS === 'android') {\n  NativeView = requireNativeView('ExpoGoogleStreetView');\n}\n\n/**\n * @platform android\n */\nexport function GoogleStreetView(props: GoogleStreetViewProps) {\n  if (!NativeView) {\n    return null;\n  }\n  return <NativeView {...props} />;\n}\n"]}