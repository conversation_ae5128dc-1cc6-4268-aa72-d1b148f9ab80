#!/usr/bin/env node

/**
 * Script de test pour vérifier la configuration Google Maps
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Lire la clé API depuis le fichier .env
function readApiKey() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const match = envContent.match(/GOOGLE_MAPS_API_KEY=(.+)/);
    return match ? match[1].trim() : null;
  } catch (error) {
    console.error('❌ Erreur lors de la lecture du fichier .env:', error.message);
    return null;
  }
}

// Tester la clé API avec l'API Geocoding
function testApiKey(apiKey) {
  return new Promise((resolve, reject) => {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=Dakar,Senegal&key=${apiKey}`;
    
    console.log('🔍 Test de la clé API Google Maps...');
    console.log('🔗 URL de test:', url.replace(apiKey, apiKey.substring(0, 10) + '...'));
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          reject(new Error('Réponse JSON invalide: ' + error.message));
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// Fonction principale
async function main() {
  console.log('🚀 Test de configuration Google Maps\n');
  
  // 1. Vérifier la clé API
  const apiKey = readApiKey();
  if (!apiKey) {
    console.error('❌ Clé API Google Maps non trouvée dans le fichier .env');
    process.exit(1);
  }
  
  console.log('✅ Clé API trouvée:', apiKey.substring(0, 10) + '...');
  
  // 2. Tester la clé API
  try {
    const response = await testApiKey(apiKey);
    
    console.log('\n📊 Résultat du test:');
    console.log('Status:', response.status);
    
    if (response.status === 'OK') {
      console.log('✅ Clé API Google Maps fonctionne correctement !');
      console.log('📍 Résultats trouvés:', response.results.length);
      if (response.results.length > 0) {
        console.log('📍 Premier résultat:', response.results[0].formatted_address);
      }
    } else {
      console.error('❌ Erreur API:', response.status);
      if (response.error_message) {
        console.error('💬 Message d\'erreur:', response.error_message);
      }
      
      // Messages d'aide selon le type d'erreur
      switch (response.status) {
        case 'REQUEST_DENIED':
          console.log('\n🔧 Solutions possibles:');
          console.log('1. Vérifiez que la clé API est correcte');
          console.log('2. Activez l\'API Geocoding dans Google Cloud Console');
          console.log('3. Vérifiez les restrictions de la clé API');
          break;
        case 'INVALID_REQUEST':
          console.log('\n🔧 La requête est invalide');
          break;
        case 'OVER_QUERY_LIMIT':
          console.log('\n🔧 Quota dépassé - vérifiez votre facturation Google Cloud');
          break;
        default:
          console.log('\n🔧 Erreur inconnue - consultez la documentation Google Maps API');
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
  
  console.log('\n📚 Ressources utiles:');
  console.log('- Google Cloud Console: https://console.cloud.google.com/');
  console.log('- Documentation Google Maps: https://developers.google.com/maps/documentation');
  console.log('- Guide de configuration: ./GOOGLE_MAPS_CONFIGURATION.md');
}

// Exécuter le script
main().catch(console.error);
