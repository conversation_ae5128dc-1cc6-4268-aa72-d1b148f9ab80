import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { OptimizedGoogleMaps } from '../components/OptimizedGoogleMaps';

export const OptimizedGoogleMapsTestScreen: React.FC = () => {
  const navigation = useNavigation();
  const [mapReady, setMapReady] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [testLocation, setTestLocation] = useState<{latitude: number, longitude: number} | null>(null);

  const testLocations = [
    { name: 'Dakar, Sénégal', lat: 14.6928, lng: -17.4467 },
    { name: 'Paris, France', lat: 48.8566, lng: 2.3522 },
    { name: 'New York, USA', lat: 40.7128, lng: -74.0060 },
    { name: 'Tokyo, Japon', lat: 35.6762, lng: 139.6503 },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Google Maps Optimisé</Text>
      </View>

      <View style={styles.mapContainer}>
        <OptimizedGoogleMaps
          style={styles.map}
          region={{
            latitude: testLocation?.latitude || 14.6928,
            longitude: testLocation?.longitude || -17.4467,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          showUserLocation={!!testLocation}
          userLocation={testLocation}
          scrollEnabled={true}
          zoomEnabled={true}
          rotateEnabled={true}
          pitchEnabled={true}
          onMapReady={() => {
            console.log('✅ Optimized Google Maps Test: Map ready');
            setMapReady(true);
          }}
          onMapLoaded={() => {
            console.log('✅ Optimized Google Maps Test: Map loaded');
            setMapLoaded(true);
          }}
        />
        
        {/* Overlay de statut */}
        <View style={styles.statusOverlay}>
          <Text style={styles.statusTitle}>🗺️ Google Maps</Text>
          <Text style={styles.statusText}>
            Provider: PROVIDER_GOOGLE
          </Text>
          <Text style={styles.statusText}>
            Ready: {mapReady ? '✅' : '⏳'}
          </Text>
          <Text style={styles.statusText}>
            Loaded: {mapLoaded ? '✅' : '⏳'}
          </Text>
          <Text style={styles.statusText}>
            Optimisé: ✅
          </Text>
        </View>
      </View>

      <ScrollView style={styles.controlsContainer}>
        <Text style={styles.controlsTitle}>🎯 Test de localisation</Text>
        
        {testLocations.map((location, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.locationButton,
              testLocation?.latitude === location.lat && styles.activeLocationButton
            ]}
            onPress={() => {
              setTestLocation({ latitude: location.lat, longitude: location.lng });
              console.log(`📍 Changement vers: ${location.name}`);
            }}
          >
            <Text style={[
              styles.locationButtonText,
              testLocation?.latitude === location.lat && styles.activeLocationButtonText
            ]}>
              📍 {location.name}
            </Text>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          style={styles.resetButton}
          onPress={() => {
            setTestLocation(null);
            console.log('🔄 Reset vers Dakar par défaut');
          }}
        >
          <Text style={styles.resetButtonText}>🔄 Reset</Text>
        </TouchableOpacity>
      </ScrollView>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          ✅ Google Maps avec PROVIDER_GOOGLE et optimisations personnalisées
        </Text>
        <Text style={styles.footerSubtext}>
          • Style personnalisé • Marqueurs colorés • Performance optimisée
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: '#4285F4',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4285F4',
  },
  mapContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  statusOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(66, 133, 244, 0.9)',
    padding: 10,
    borderRadius: 8,
  },
  statusTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusText: {
    color: 'white',
    fontSize: 11,
    marginBottom: 2,
  },
  controlsContainer: {
    maxHeight: 200,
    backgroundColor: '#f8f9fa',
    padding: 15,
  },
  controlsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  locationButton: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeLocationButton: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
  },
  locationButtonText: {
    fontSize: 14,
    color: '#333',
  },
  activeLocationButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: '#FF6B6B',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  resetButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  footer: {
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  footerText: {
    textAlign: 'center',
    color: '#4285F4',
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 4,
  },
  footerSubtext: {
    textAlign: 'center',
    color: '#666',
    fontSize: 11,
  },
});
