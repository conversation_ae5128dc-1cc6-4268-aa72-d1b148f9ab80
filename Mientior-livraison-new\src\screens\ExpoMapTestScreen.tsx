import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MapView, Marker } from 'expo-maps';

const { width, height } = Dimensions.get('window');

export const ExpoMapTestScreen: React.FC = () => {
  const navigation = useNavigation();
  const [mapReady, setMapReady] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Test Expo Maps</Text>
      </View>

      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          provider="google"
          initialRegion={{
            latitude: 14.6928, // Dakar
            longitude: -17.4467,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          onMapReady={() => {
            console.log('✅ Expo Maps Ready');
            setMapReady(true);
          }}
          onMapLoaded={() => {
            console.log('✅ Expo Maps Loaded');
            setMapLoaded(true);
          }}
        >
          <Marker
            coordinate={{
              latitude: 14.6928,
              longitude: -17.4467,
            }}
            title="Dakar, Sénégal"
            description="Test Expo Maps"
          />
        </MapView>
        
        {/* Overlay de statut */}
        <View style={styles.statusOverlay}>
          <Text style={styles.statusText}>
            📦 Expo Maps
          </Text>
          <Text style={styles.statusText}>
            Ready: {mapReady ? '✅' : '⏳'}
          </Text>
          <Text style={styles.statusText}>
            Loaded: {mapLoaded ? '✅' : '⏳'}
          </Text>
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          🎯 Si vous voyez une carte Google Maps ci-dessus, Expo Maps fonctionne parfaitement !
        </Text>
        <Text style={styles.footerSubtext}>
          Expo Maps est plus stable et mieux intégré avec Expo que react-native-maps.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  mapContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  statusOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 10,
    borderRadius: 5,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    marginBottom: 2,
  },
  footer: {
    padding: 20,
    backgroundColor: '#f8f8f8',
  },
  footerText: {
    textAlign: 'center',
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  footerSubtext: {
    textAlign: 'center',
    color: '#666',
    fontSize: 12,
  },
});
