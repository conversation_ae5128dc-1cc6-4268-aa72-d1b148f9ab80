/**
 * The type of map to display.
 * @platform android
 */
export var GoogleMapsMapType;
(function (GoogleMapsMapType) {
    /**
     * Satellite imagery with roads and points of interest overlayed.
     */
    GoogleMapsMapType["HYBRID"] = "HYBRID";
    /**
     * Standard road map.
     */
    GoogleMapsMapType["NORMAL"] = "NORMAL";
    /**
     * Satellite imagery.
     */
    GoogleMapsMapType["SATELLITE"] = "SATELLITE";
    /**
     * Topographic data.
     */
    GoogleMapsMapType["TERRAIN"] = "TERRAIN";
})(GoogleMapsMapType || (GoogleMapsMapType = {}));
/**
 * @platform android
 */
export var GoogleMapsColorScheme;
(function (GoogleMapsColorScheme) {
    GoogleMapsColorScheme["LIGHT"] = "LIGHT";
    GoogleMapsColorScheme["DARK"] = "DARK";
    GoogleMapsColorScheme["FOLLOW_SYSTEM"] = "FOLLOW_SYSTEM";
})(GoogleMapsColorScheme || (GoogleMapsColorScheme = {}));
//# sourceMappingURL=GoogleMaps.types.js.map