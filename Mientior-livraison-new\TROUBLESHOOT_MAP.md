# 🔧 Guide de dépannage Google Maps

## ✅ État actuel de la configuration

Votre configuration Google Maps est **CORRECTE** :
- ✅ Clé API configurée et fonctionnelle
- ✅ Restrictions de sécurité corrigées
- ✅ Configuration app.config.js OK
- ✅ Plugin Google Maps OK
- ✅ Dépendances installées

## 🎯 Solutions pour afficher la carte

### 1. Redémarrage complet de l'application

```bash
# Arrêter l'application (Ctrl+C)
# Puis redémarrer avec cache clear
npx expo start --clear
```

### 2. Test de la carte simple

Dans l'application, utilisez le bouton **"🗺️ Test carte simple"** pour vérifier si Google Maps fonctionne de base.

### 3. Vérification des logs

Ouvrez les outils de développement de votre navigateur ou l'application Expo Go et regardez les logs console pour :
- `✅ Google Maps loaded successfully`
- `🔑 Google Maps API Key configured: Yes`
- Erreurs éventuelles

### 4. Forcer l'affichage

Utilisez le bouton **"🔧 Forcer l'affichage de la carte"** dans l'écran de permission.

## 🔍 Diagnostic des problèmes courants

### Problème : Carte blanche ou vide
**Causes possibles :**
- Délai de chargement trop long
- Problème de rendu React Native
- Conflit avec les animations

**Solutions :**
1. Augmenter le timeout (déjà fait : 30 secondes)
2. Désactiver temporairement les animations
3. Utiliser le test de carte simple

### Problème : Indicateur de chargement permanent
**Causes possibles :**
- Clé API non reconnue par l'application
- Problème de configuration Expo

**Solutions :**
1. Vérifier que la clé API est bien chargée dans l'app
2. Redémarrer avec `--clear`
3. Vérifier les logs console

### Problème : Erreur "Map timeout"
**Causes possibles :**
- Connexion internet lente
- Serveurs Google Maps temporairement indisponibles

**Solutions :**
1. Vérifier la connexion internet
2. Attendre et réessayer
3. Utiliser le mode fallback temporairement

## 🚀 Commandes de test

```bash
# Test de la clé API
node quick-test-maps.js

# Vérification complète de la config
node check-map-config.js

# Redémarrage avec logs détaillés
npx expo start --clear --verbose
```

## 📱 Test sur différentes plateformes

1. **Expo Go (Développement)** : Devrait fonctionner immédiatement
2. **Build Android** : Nécessite la clé API dans le manifest
3. **Build iOS** : Nécessite la clé API dans Info.plist

## 🔄 Si rien ne fonctionne

1. **Créer une nouvelle clé API** :
   - Allez sur Google Cloud Console
   - Créez une nouvelle clé sans restrictions
   - Remplacez dans `.env`

2. **Vérifier les quotas** :
   - Vérifiez que vous n'avez pas dépassé les quotas gratuits
   - Activez la facturation si nécessaire

3. **Tester avec une carte basique** :
   - Utilisez le composant `SimpleMapTest`
   - Vérifiez si le problème vient de la complexité du composant

## 📞 Support

Si le problème persiste :
1. Vérifiez les logs détaillés
2. Testez sur un autre appareil/émulateur
3. Consultez la documentation Expo Maps
4. Vérifiez les issues GitHub de react-native-maps
