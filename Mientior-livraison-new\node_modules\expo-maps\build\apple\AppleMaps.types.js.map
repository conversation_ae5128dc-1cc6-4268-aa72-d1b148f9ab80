{"version": 3, "file": "AppleMaps.types.js", "sourceRoot": "", "sources": ["../../src/apple/AppleMaps.types.ts"], "names": [], "mappings": "AA8DA;;;GAGG;AACH,MAAM,CAAN,IAAY,gBAaX;AAbD,WAAY,gBAAgB;IAC1B;;OAEG;IACH,qCAAiB,CAAA;IACjB;;OAEG;IACH,yCAAqB,CAAA;IACrB;;OAEG;IACH,uCAAmB,CAAA;AACrB,CAAC,EAbW,gBAAgB,KAAhB,gBAAgB,QAa3B;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,qBASX;AATD,WAAY,qBAAqB;IAC/B;;OAEG;IACH,8CAAqB,CAAA;IACrB;;OAEG;IACH,8CAAqB,CAAA;AACvB,CAAC,EATW,qBAAqB,KAArB,qBAAqB,QAShC", "sourcesContent": ["import type { SharedRefType } from 'expo';\nimport type { Ref } from 'react';\nimport type { ProcessedColorValue, StyleProp, ViewStyle } from 'react-native';\n\nimport { CameraPosition, Coordinates } from '../shared.types';\n\n/**\n * @platform ios\n */\nexport type AppleMapsMarker = {\n  /**\n   * The unique identifier for the marker. This can be used to identify the clicked marker in the `onMarkerClick` event.\n   */\n  id?: string;\n\n  /**\n   * The SF Symbol to display for the marker.\n   */\n  systemImage?: string;\n\n  /**\n   * The coordinates of the marker.\n   */\n  coordinates?: Coordinates;\n\n  /**\n   * The title of the marker, displayed in the callout when the marker is clicked.\n   */\n  title?: string;\n\n  /**\n   * The tint color of the marker.\n   */\n  tintColor?: string;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsUISettings = {\n  /**\n   * Whether the compass is enabled on the map.\n   * If enabled, the compass is only visible when the map is rotated.\n   */\n  compassEnabled?: boolean;\n\n  /**\n   * Whether the my location button is visible.\n   */\n  myLocationButtonEnabled?: boolean;\n\n  /**\n   * Whether the scale bar is displayed when zooming.\n   */\n  scaleBarEnabled?: boolean;\n\n  /**\n   * Whether the user is allowed to change the pitch type.\n   */\n  togglePitchEnabled?: boolean;\n};\n\n/**\n * The type of map to display.\n * @platform ios\n */\nexport enum AppleMapsMapType {\n  /**\n   * A satellite image of the area with road and road name layers on top.\n   */\n  HYBRID = 'HYBRID',\n  /**\n   * A street map that shows the position of all roads and some road names.\n   */\n  STANDARD = 'STANDARD',\n  /**\n   * A satellite image of the area.\n   */\n  IMAGERY = 'IMAGERY',\n}\n\n/**\n * The style of the polyline.\n * @platform ios\n */\nexport enum AppleMapsContourStyle {\n  /**\n   * A straight line.\n   */\n  STRAIGHT = 'STRAIGHT',\n  /**\n   * A geodesic line.\n   */\n  GEODESIC = 'GEODESIC',\n}\n\n/**\n * @platform ios\n */\nexport type AppleMapsProperties = {\n  /**\n   * Whether the traffic layer is enabled on the map.\n   */\n  isTrafficEnabled?: boolean;\n\n  /**\n   * Defines which map type should be used.\n   */\n  mapType?: AppleMapsMapType;\n\n  /**\n   * If true, the user can select a location on the map to get more information.\n   */\n  selectionEnabled?: boolean;\n\n  /**\n   * The maximum distance in meters from a tap of a polyline for it to be considered a hit.\n   * If the distance is greater than the threshold, the polyline is not considered a hit.\n   * If a hit occurs, the `onPolylineClick` event will be triggered.\n   * Defaults to 20 meters.\n   */\n  polylineTapThreshold?: number;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsAnnotation = {\n  /**\n   * The background color of the annotation.\n   */\n  backgroundColor?: string;\n  /**\n   * The text to display in the annotation.\n   */\n  text?: string;\n  /**\n   * The text color of the annotation.\n   */\n  textColor?: string;\n  /**\n   * The custom icon to display in the annotation.\n   */\n  icon?: SharedRefType<'image'>;\n} & AppleMapsMarker;\n\n/**\n * @platform ios\n */\nexport type AppleMapsPolyline = {\n  /**\n   * The unique identifier for the polyline. This can be used to identify the clicked polyline in the `onPolylineClick` event.\n   */\n  id?: string;\n  /**\n   * The coordinates of the polyline.\n   */\n  coordinates: Coordinates[];\n  /**\n   * The color of the polyline.\n   */\n  color?: ProcessedColorValue | string;\n  /**\n   * The width of the polyline.\n   */\n  width?: number;\n  /**\n   * The style of the polyline.\n   */\n  contourStyle?: AppleMapsContourStyle;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsCircle = {\n  /**\n   * The unique identifier for the circle. This can be used to identify the clicked circle in the `onCircleClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the circle.\n   */\n  center: Coordinates;\n\n  /**\n   * The radius of the circle (in meters).\n   */\n  radius: number;\n\n  /**\n   * The color of the circle.\n   */\n  color?: ProcessedColorValue | string;\n\n  /**\n   * The width of the circle.\n   */\n  width?: number;\n\n  /**\n   * The color of the circle line.\n   */\n  lineColor?: ProcessedColorValue | string;\n\n  /**\n   * The width of the circle line.\n   */\n  lineWidth?: number;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsPolygon = {\n  /**\n   * The unique identifier for the polygon. This can be used to identify the clicked polygon in the `onPolygonClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the circle.\n   */\n  coordinates: Coordinates[];\n\n  /**\n   * The color of the polygon.\n   */\n  color?: ProcessedColorValue | string;\n\n  /**\n   * The width of the polygon.\n   */\n  lineWidth?: number;\n\n  /**\n   * The color of the polygon.\n   */\n  lineColor?: ProcessedColorValue | string;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsViewProps = {\n  ref?: Ref<AppleMapsViewType>;\n  style?: StyleProp<ViewStyle>;\n\n  /**\n   * The initial camera position of the map.\n   */\n  cameraPosition?: CameraPosition;\n\n  /**\n   * The array of markers to display on the map.\n   */\n  markers?: AppleMapsMarker[];\n\n  /**\n   * The array of polylines to display on the map.\n   */\n  polylines?: AppleMapsPolyline[];\n\n  /**\n   * The array of polygons to display on the map.\n   */\n  polygons?: AppleMapsPolygon[];\n\n  /**\n   * The array of circles to display on the map.\n   */\n  circles?: AppleMapsCircle[];\n\n  /**\n   * The array of annotations to display on the map.\n   */\n  annotations?: AppleMapsAnnotation[];\n\n  /**\n   * The `MapUiSettings` to be used for UI-specific settings on the map.\n   */\n  uiSettings?: AppleMapsUISettings;\n\n  /**\n   * The properties for the map.\n   */\n  properties?: AppleMapsProperties;\n\n  /**\n   * Lambda invoked when the user clicks on the map.\n   * It won't be invoked if the user clicks on POI or a marker.\n   */\n  onMapClick?: (event: { coordinates: Coordinates }) => void;\n\n  /**\n   * Lambda invoked when the marker is clicked\n   * @platform ios 18.0+\n   */\n  onMarkerClick?: (event: AppleMapsMarker) => void;\n\n  /**\n   * Lambda invoked when the polyline is clicked\n   * @platform ios 18.0+\n   */\n  onPolylineClick?: (event: AppleMapsPolyline) => void;\n\n  /**\n   * Lambda invoked when the polygon is clicked\n   * @platform ios 18.0+\n   */\n  onPolygonClick?: (event: AppleMapsPolygon) => void;\n\n  /**\n   * Lambda invoked when the circle is clicked\n   * @platform ios 18.0+\n   */\n  onCircleClick?: (event: AppleMapsCircle) => void;\n\n  /**\n   * Lambda invoked when the map was moved by the user.\n   */\n  onCameraMove?: (event: {\n    coordinates: Coordinates;\n    zoom: number;\n    tilt: number;\n    bearing: number;\n  }) => void;\n};\n\n/**\n * @platform ios\n */\nexport type AppleMapsViewType = {\n  /**\n   * Update camera position.\n   * Animation duration is not supported on iOS.\n   *\n   * @param config New camera postion.\n   */\n  setCameraPosition: (config?: CameraPosition) => void;\n\n  /**\n   * Opens the look around view at specified coordinates.\n   *\n   * @param coordinates The coordinates of the location to open the look around view at.\n   */\n  openLookAroundAsync: (coordinates: Coordinates) => Promise<void>;\n};\n"]}