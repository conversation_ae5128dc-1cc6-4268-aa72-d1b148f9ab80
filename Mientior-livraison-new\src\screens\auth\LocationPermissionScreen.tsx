import React, { useState, useRef, useEffect } from 'react';
import Constants from 'expo-constants';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  Animated,
  Dimensions,
  ScrollView,
  Platform,
  AccessibilityInfo,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
// import { LinearGradient } from 'expo-linear-gradient'; // Temporairement désactivé
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
// import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps'; // Remplacé par BasicMapView
import { colors, shadows, spacing } from '../../constants/theme';
import { Button } from '../../components/ui/Button';
// import { BasicMapView } from '../../components/BasicMapView'; // Remplacé par ExpoMapView
// import { ExpoMapView } from '../../components/ExpoMapView'; // Remplacé par OptimizedGoogleMaps
import { OptimizedGoogleMaps } from '../../components/OptimizedGoogleMaps';

const { height: screenHeight } = Dimensions.get('window');

export const LocationPermissionScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [mapError, setMapError] = useState(false);
  const [forceMapDisplay, setForceMapDisplay] = useState(true); // Force map display
  const [mapLoaded, setMapLoaded] = useState(false);
  const [showMap, setShowMap] = useState(true); // Always show map by default
  const [mapRegion, setMapRegion] = useState({
    latitude: 14.6928, // Dakar, Sénégal par défaut
    longitude: -17.4467,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;
  const mapScaleAnim = useRef(new Animated.Value(1)).current;

  // Ref to store the map timeout
  const mapTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check Google Maps API Key
    const googleMapsApiKey = Constants.expoConfig?.extra?.googleMapsApiKey;
    console.log('🔑 Google Maps API Key configured:', googleMapsApiKey ? 'Yes' : 'No');
    console.log('🔑 API Key value:', googleMapsApiKey ? `${googleMapsApiKey.substring(0, 10)}...` : 'None');
    console.log('🔑 Full API Key for debug:', googleMapsApiKey);

    // Force map to show - we'll handle API key issues in the map component itself
    console.log('🚀 Forcing map display - API key will be handled by react-native-maps');
    console.log('🗺️ Map state - Error:', mapError, 'Force display:', forceMapDisplay, 'Show map:', showMap);
    setMapError(false); // Always try to show the map first
    setForceMapDisplay(true); // Ensure map is forced to display
    setShowMap(true); // Always show the map

    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Icon rotation animation
    Animated.loop(
      Animated.timing(iconRotateAnim, {
        toValue: 1,
        duration: 8000,
        useNativeDriver: true,
      })
    ).start();

    // Pulse animation for location icon
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Subtle map zoom animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(mapScaleAnim, {
          toValue: 1.05,
          duration: 10000,
          useNativeDriver: true,
        }),
        Animated.timing(mapScaleAnim, {
          toValue: 1,
          duration: 10000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Check current permission status
    checkPermissionStatus();

    // Timeout désactivé temporairement pour forcer l'affichage de la carte
    // mapTimeoutRef.current = setTimeout(() => {
    //   console.log('⏰ Map timeout reached after 30 seconds, showing fallback');
    //   setMapError(true);
    // }, 30000); // 30 seconds timeout - give more time for map to load

    return () => {
      if (mapTimeoutRef.current) {
        clearTimeout(mapTimeoutRef.current);
      }
    };
  }, []);

  const handleMapReady = () => {
    console.log('✅ Google Maps loaded successfully - Real map is now displayed');
    console.log('📍 Map region:', mapRegion);
    console.log('👤 User location:', userLocation ? 'Available' : 'Not available');

    // Map loaded successfully, clear timeout and ensure mapError is false
    if (mapTimeoutRef.current) {
      clearTimeout(mapTimeoutRef.current);
      mapTimeoutRef.current = null;
      console.log('⏰ Map timeout cleared');
    }
    setMapError(false);
    console.log('🗺️ Map error state set to false - Real Google Maps is visible');
  };

  const checkPermissionStatus = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      console.log('Current permission status:', status);
    } catch (error) {
      console.log('Error checking permission status:', error);
    }
  };

  const handleRequestPermission = async () => {
    try {
      setLoading(true);

      // Demander la permission de géolocalisation
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('Permission request result:', status);
      
      if (status === 'granted') {
        // Permission accordée, obtenir la position actuelle
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          });
          
          console.log('Position obtenue:', location.coords);
          
          // Mettre à jour la position de l'utilisateur et la région de la carte
          setUserLocation(location);
          setMapRegion({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          
          // Announce success for accessibility
          AccessibilityInfo.announceForAccessibility('Permission de géolocalisation accordée');
          
          Alert.alert(
            '🎯 Permission accordée !',
            'Parfait ! Nous pourrons vous proposer les meilleurs restaurants et services près de chez vous.',
            [{ 
              text: 'Continuer', 
              onPress: () => {
                // Success animation before navigation
                Animated.sequence([
                  Animated.timing(scaleAnim, {
                    toValue: 1.05,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                  Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                ]).start(() => {
                  navigation.navigate('AuthChoiceScreen' as never);
                });
              }
            }]
          );
        } catch (locationError) {
          console.error('Erreur lors de l\'obtention de la position:', locationError);
          // Même si on ne peut pas obtenir la position, on continue
          navigation.navigate('AuthChoiceScreen' as never);
        }
      } else {
        // Permission refusée
        Alert.alert(
          '📍 Permission refusée',
          'Pas de problème ! Vous pourrez toujours utiliser l\'application en saisissant votre adresse manuellement.',
          [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
        );
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      Alert.alert(
        '⚠️ Erreur',
        'Une erreur est survenue. Vous pourrez configurer la géolocalisation plus tard dans les paramètres.',
        [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    Alert.alert(
      '🤔 Ignorer la géolocalisation',
      'Vous pourrez activer la géolocalisation plus tard dans les paramètres pour une meilleure expérience.',
      [
        { text: 'Retour', style: 'cancel' },
        { text: 'Ignorer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="transparent" translucent barStyle="dark-content" />
      
      {/* Google Map Background */}
      <Animated.View style={[styles.mapContainer, {
        transform: [{ scale: mapScaleAnim }]
      }]}>
        <OptimizedGoogleMaps
          style={styles.mapImage}
          region={mapRegion}
          showUserLocation={!!userLocation}
          userLocation={userLocation ? {
            latitude: userLocation.coords.latitude,
            longitude: userLocation.coords.longitude,
          } : null}
          onMapReady={() => {
            console.log('✅ Optimized Google Maps ready');
            setMapLoaded(true);
          }}
          onMapLoaded={() => {
            console.log('✅ Optimized Google Maps loaded');
          }}
        />
        <View style={styles.mapOverlay} />
        {/* Pin de localisation par défaut - seulement si pas de vraie carte */}
        {mapError && !userLocation && (
          <Animated.View style={[styles.locationPin, {
            transform: [{ scale: pulseAnim }]
          }]}>
            <View style={styles.pinOuter}>
              <View style={styles.pinInner} />
            </View>
            <View style={styles.pinShadow} />
          </Animated.View>
        )}
      </Animated.View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ],
            },
          ]}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.title}>Autoriser la localisation</Text>
            <Text style={styles.subtitle}>
              Pour une meilleure expérience de livraison, permettez-nous d'accéder à votre position
            </Text>
            
            <View style={styles.benefitsList}>
              <View style={styles.benefitItem}>
                <View style={styles.benefitIconContainer}>
                  <Ionicons name="location" size={24} color={colors.primary[500]} />
                </View>
                <Text style={styles.benefitText}>
                  Livraison précise à votre porte
                </Text>
              </View>
              
              <View style={styles.benefitItem}>
                <View style={styles.benefitIconContainer}>
                  <Ionicons name="time" size={24} color={colors.primary[500]} />
                </View>
                <Text style={styles.benefitText}>
                  Estimation du temps plus précise
                </Text>
              </View>
              
              <View style={styles.benefitItem}>
                <View style={styles.benefitIconContainer}>
                  <Ionicons name="navigate" size={24} color={colors.primary[500]} />
                </View>
                <Text style={styles.benefitText}>
                  Suivi en temps réel du livreur
                </Text>
              </View>
            </View>

            <Button
              title="Autoriser l'accès"
              onPress={handleRequestPermission}
              disabled={loading}
              loading={loading}
              variant="primary"
              size="lg"
              gradient={true}
              fullWidth={true}
              style={styles.allowButton}
              accessibilityLabel="Autoriser l'accès à la localisation"
              accessibilityHint="Appuyez pour autoriser l'accès à votre position"
            />
            
            <TouchableOpacity
              style={styles.skipButton}
              onPress={handleSkip}
              disabled={loading}
              accessibilityLabel="Plus tard"
              accessibilityRole="button"
            >
              <Text style={styles.skipButtonText}>Plus tard</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.manualAddressButton}
              onPress={() => navigation.navigate('ManualAddressScreen' as never)}
              accessibilityLabel="Saisir mon adresse manuellement"
              accessibilityRole="button"
            >
              <Text style={styles.manualAddressText}>Saisir mon adresse manuellement</Text>
            </TouchableOpacity>

            {/* Debug info - remove in production */}
            <View style={styles.debugInfo}>
              <Text style={styles.debugText}>
                🗺️ État: {mapError ? 'Mode Fallback' : (mapLoaded ? 'Google Maps Optimisé ✅' : 'Google Maps en cours...')}
              </Text>
              <Text style={styles.debugText}>
                📍 Position: {userLocation ? 'Utilisateur' : 'Dakar par défaut'}
              </Text>
              <Text style={styles.debugText}>
                🔧 Force Display: {forceMapDisplay ? 'Oui' : 'Non'} | Error: {mapError ? 'Oui' : 'Non'} | Show: {showMap ? 'Oui' : 'Non'}
              </Text>
            </View>

            {/* Debug buttons - remove in production */}
            <TouchableOpacity
              style={[styles.skipButton, { backgroundColor: colors.warning[500] }]}
              onPress={() => {
                console.log('🔧 Debug: Forcing map display');
                setMapError(false);
                setForceMapDisplay(true);
                setShowMap(true);
              }}
            >
              <Text style={[styles.skipButtonText, { color: 'white' }]}>🔧 Forcer l'affichage de la carte</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.skipButton, { backgroundColor: colors.primary[500] }]}
              onPress={() => {
                navigation.navigate('SimpleMapTestScreen' as never);
              }}
            >
              <Text style={[styles.skipButtonText, { color: 'white' }]}>🗺️ Test react-native-maps</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.skipButton, { backgroundColor: colors.success[500] }]}
              onPress={() => {
                navigation.navigate('ExpoMapTestScreen' as never);
              }}
            >
              <Text style={[styles.skipButtonText, { color: 'white' }]}>📦 Test Expo Maps</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.skipButton, { backgroundColor: '#4285F4' }]}
              onPress={() => {
                console.log('🗺️ Google Maps Optimisé est maintenant actif !');
                console.log('✅ Configuration: PROVIDER_GOOGLE avec optimisations');
              }}
            >
              <Text style={[styles.skipButtonText, { color: 'white' }]}>🗺️ Google Maps Optimisé Actif</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.bottomIndicator} />
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  mapContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: screenHeight * 0.4,
    overflow: 'hidden',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  mapFallback: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapErrorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mapErrorText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  mapErrorSubtext: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  mapOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.05)', // Reduced opacity to see map better
  },
  locationPin: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -30,
    alignItems: 'center',
  },
  pinOuter: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.md,
  },
  pinInner: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    backgroundColor: 'white',
  },
  pinShadow: {
    width: 15,
    height: 5,
    borderRadius: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    marginTop: 3,
    transform: [{ scaleX: 1.5 }],
  },
  customMarker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerOuter: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
    ...shadows.lg,
  },
  markerInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'white',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: screenHeight * 0.35,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.xl,
    ...shadows.lg,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: spacing.md,
    marginBottom: spacing.xl,
  },
  benefitsList: {
    marginBottom: spacing.xl,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  benefitIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(90, 148, 92, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  benefitText: {
    fontSize: 16,
    color: colors.text.primary,
    flex: 1,
  },
  allowButton: {
    marginBottom: spacing.lg,
    borderRadius: 12,
    height: 56,
    backgroundColor: colors.primary[500],
  },
  skipButton: {
    paddingVertical: spacing.md,
    alignItems: 'center',
    marginBottom: spacing.md,
    height: 56,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: 12,
    justifyContent: 'center',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  manualAddressButton: {
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  manualAddressText: {
    fontSize: 14,
    color: colors.text.secondary,
    textDecorationLine: 'underline',
  },
  bottomIndicator: {
    width: 40,
    height: 5,
    backgroundColor: colors.neutral[300],
    borderRadius: 2.5,
    alignSelf: 'center',
    marginTop: spacing.md,
  },
  debugInfo: {
    backgroundColor: colors.neutral[100],
    borderRadius: 8,
    padding: spacing.sm,
    marginBottom: spacing.sm,
  },
  debugText: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
});

export default LocationPermissionScreen;
