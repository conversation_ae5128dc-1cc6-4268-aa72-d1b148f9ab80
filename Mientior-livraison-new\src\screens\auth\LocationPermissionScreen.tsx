import React, { useState, useRef, useEffect } from 'react';
import Constants from 'expo-constants';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  Animated,
  Dimensions,
  Platform,
  AccessibilityInfo,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { colors, shadows, spacing } from '../../constants/theme';
import { OptimizedGoogleMaps } from '../../components/OptimizedGoogleMaps';

const { height: screenHeight } = Dimensions.get('window');

export const LocationPermissionScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [mapError, setMapError] = useState(false);
  const [forceMapDisplay, setForceMapDisplay] = useState(true); // Force map display
  const [mapLoaded, setMapLoaded] = useState(false);
  const [showMap, setShowMap] = useState(true); // Always show map by default
  const [useStaticMap, setUseStaticMap] = useState(false); // Fallback to static map
  const [mapRegion, setMapRegion] = useState({
    latitude: 14.6928, // Dakar, Sénégal par défaut
    longitude: -17.4467,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;
  const mapScaleAnim = useRef(new Animated.Value(1)).current;

  // Ref to store the map timeout
  const mapTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check Google Maps API Key
    const googleMapsApiKey = Constants.expoConfig?.extra?.googleMapsApiKey;
    console.log('🔑 Google Maps API Key configured:', googleMapsApiKey ? 'Yes' : 'No');
    console.log('🔑 API Key value:', googleMapsApiKey ? `${googleMapsApiKey.substring(0, 10)}...` : 'None');
    console.log('🔑 Full API Key for debug:', googleMapsApiKey);

    // Force map to show - we'll handle API key issues in the map component itself
    console.log('🚀 Forcing map display - API key will be handled by react-native-maps');
    console.log('🗺️ Map state - Error:', mapError, 'Force display:', forceMapDisplay, 'Show map:', showMap);
    setMapError(false); // Always try to show the map first
    setForceMapDisplay(true); // Ensure map is forced to display
    setShowMap(true); // Always show the map

    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Icon rotation animation
    Animated.loop(
      Animated.timing(iconRotateAnim, {
        toValue: 1,
        duration: 8000,
        useNativeDriver: true,
      })
    ).start();

    // Pulse animation for location icon
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Subtle map zoom animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(mapScaleAnim, {
          toValue: 1.05,
          duration: 10000,
          useNativeDriver: true,
        }),
        Animated.timing(mapScaleAnim, {
          toValue: 1,
          duration: 10000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Check current permission status
    checkPermissionStatus();

    // Timeout désactivé temporairement pour forcer l'affichage de la carte
    // mapTimeoutRef.current = setTimeout(() => {
    //   console.log('⏰ Map timeout reached after 30 seconds, showing fallback');
    //   setMapError(true);
    // }, 30000); // 30 seconds timeout - give more time for map to load

    return () => {
      if (mapTimeoutRef.current) {
        clearTimeout(mapTimeoutRef.current);
      }
    };
  }, []);

  const handleMapReady = () => {
    console.log('✅ Google Maps loaded successfully - Real map is now displayed');
    console.log('📍 Map region:', mapRegion);
    console.log('👤 User location:', userLocation ? 'Available' : 'Not available');

    // Map loaded successfully, clear timeout and ensure mapError is false
    if (mapTimeoutRef.current) {
      clearTimeout(mapTimeoutRef.current);
      mapTimeoutRef.current = null;
      console.log('⏰ Map timeout cleared');
    }
    setMapError(false);
    console.log('🗺️ Map error state set to false - Real Google Maps is visible');
  };

  const checkPermissionStatus = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      console.log('Current permission status:', status);
    } catch (error) {
      console.log('Error checking permission status:', error);
    }
  };

  const handleRequestPermission = async () => {
    try {
      setLoading(true);

      // Demander la permission de géolocalisation
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('Permission request result:', status);
      
      if (status === 'granted') {
        // Permission accordée, obtenir la position actuelle
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          });
          
          console.log('Position obtenue:', location.coords);
          
          // Mettre à jour la position de l'utilisateur et la région de la carte
          setUserLocation(location);
          setMapRegion({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          
          // Announce success for accessibility
          AccessibilityInfo.announceForAccessibility('Permission de géolocalisation accordée');
          
          Alert.alert(
            '🎯 Permission accordée !',
            'Parfait ! Nous pourrons vous proposer les meilleurs restaurants et services près de chez vous.',
            [{ 
              text: 'Continuer', 
              onPress: () => {
                // Success animation before navigation
                Animated.sequence([
                  Animated.timing(scaleAnim, {
                    toValue: 1.05,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                  Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                ]).start(() => {
                  navigation.navigate('AuthChoiceScreen' as never);
                });
              }
            }]
          );
        } catch (locationError) {
          console.error('Erreur lors de l\'obtention de la position:', locationError);
          // Même si on ne peut pas obtenir la position, on continue
          navigation.navigate('AuthChoiceScreen' as never);
        }
      } else {
        // Permission refusée
        Alert.alert(
          '📍 Permission refusée',
          'Pas de problème ! Vous pourrez toujours utiliser l\'application en saisissant votre adresse manuellement.',
          [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
        );
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      Alert.alert(
        '⚠️ Erreur',
        'Une erreur est survenue. Vous pourrez configurer la géolocalisation plus tard dans les paramètres.',
        [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    Alert.alert(
      '🤔 Ignorer la géolocalisation',
      'Vous pourrez activer la géolocalisation plus tard dans les paramètres pour une meilleure expérience.',
      [
        { text: 'Retour', style: 'cancel' },
        { text: 'Ignorer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="transparent" translucent barStyle="dark-content" />

      {/* Google Map Background - Full Screen */}
      <View style={styles.mapBackground}>
        {/* Carte statique Google Maps identique à l'image */}
        <Image
          source={{
            uri: `https://maps.googleapis.com/maps/api/staticmap?center=48.8566,2.3522&zoom=16&size=400x800&maptype=roadmap&key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&style=feature:all%7Celement:labels%7Cvisibility:on&style=feature:all%7Celement:labels.text.fill%7Ccolor:0x616161&style=feature:all%7Celement:labels.text.stroke%7Ccolor:0xffffff&style=feature:administrative%7Celement:geometry.fill%7Ccolor:0xfefefe&style=feature:administrative%7Celement:geometry.stroke%7Ccolor:0xfefefe&style=feature:landscape%7Celement:geometry%7Ccolor:0xf5f5f5&style=feature:poi%7Celement:geometry%7Ccolor:0xf5f5f5&style=feature:poi.park%7Celement:geometry%7Ccolor:0xdedede&style=feature:road%7Celement:geometry%7Ccolor:0xffffff&style=feature:road%7Celement:geometry.stroke%7Ccolor:0xe9e9e9&style=feature:road.arterial%7Celement:geometry%7Ccolor:0xffffff&style=feature:road.highway%7Celement:geometry%7Ccolor:0xffffff&style=feature:road.highway%7Celement:geometry.stroke%7Ccolor:0xe9e9e9&style=feature:road.local%7Celement:geometry%7Ccolor:0xffffff&style=feature:transit.line%7Celement:geometry%7Ccolor:0xe5e5e5&style=feature:water%7Celement:geometry%7Ccolor:0xc9c9c9`
          }}
          style={styles.mapFullScreen}
          resizeMode="cover"
        />

        {/* Pin de localisation personnalisé au centre */}
        <View style={styles.centerPin}>
          <View style={styles.pinContainer}>
            <View style={styles.pinOuter}>
              <View style={styles.pinInner} />
            </View>
          </View>
        </View>
      </View>

      {/* Content Overlay */}
      <View style={styles.contentOverlay}>
        <Animated.View
          style={[
            styles.contentContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ],
            },
          ]}
        >
          {/* Title */}
          <Text style={styles.title}>Autoriser la localisation</Text>

          {/* Subtitle */}
          <Text style={styles.subtitle}>
            Pour une meilleure expérience de livraison, permettez-nous d'accéder à votre position
          </Text>

          {/* Benefits List */}
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="location-outline" size={20} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Livraison précise à votre porte
              </Text>
            </View>

            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="time-outline" size={20} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Estimation du temps plus précise
              </Text>
            </View>

            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="navigate-outline" size={20} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Suivi en temps réel du livreur
              </Text>
            </View>
          </View>

          {/* Primary Button */}
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleRequestPermission}
            disabled={loading}
            accessibilityLabel="Autoriser l'accès à la localisation"
          >
            <Text style={styles.primaryButtonText}>
              {loading ? 'Chargement...' : 'Autoriser l\'accès'}
            </Text>
          </TouchableOpacity>

          {/* Secondary Button */}
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleSkip}
            disabled={loading}
            accessibilityLabel="Plus tard"
          >
            <Text style={styles.secondaryButtonText}>Plus tard</Text>
          </TouchableOpacity>

          {/* Manual Address Link */}
          <TouchableOpacity
            style={styles.manualAddressButton}
            onPress={() => navigation.navigate('ManualAddressScreen' as never)}
            accessibilityLabel="Saisir mon adresse manuellement"
          >
            <Text style={styles.manualAddressText}>Saisir mon adresse manuellement</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  mapBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  mapFullScreen: {
    width: '100%',
    height: '100%',
  },
  contentOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: 50,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  mapFallback: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapErrorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mapErrorText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  mapErrorSubtext: {
    fontSize: 14,
    color: colors.text.secondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  mapOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.05)', // Reduced opacity to see map better
  },
  locationPin: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -30,
    alignItems: 'center',
  },
  pinOuter: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.md,
  },
  pinInner: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    backgroundColor: 'white',
  },
  pinShadow: {
    width: 15,
    height: 5,
    borderRadius: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    marginTop: 3,
    transform: [{ scaleX: 1.5 }],
  },
  customMarker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerOuter: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
    ...shadows.lg,
  },
  markerInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'white',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: screenHeight * 0.35,
  },

  benefitsList: {
    marginBottom: 40,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  benefitIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(13, 202, 168, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  benefitText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
    lineHeight: 24,
  },
  primaryButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 16,
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0DCAA8',
    textAlign: 'center',
  },

  manualAddressButton: {
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  manualAddressText: {
    fontSize: 14,
    color: '#6B7280',
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
  debugOverlay: {
    position: 'absolute',
    top: 50,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 10,
    zIndex: 1000,
  },
  debugText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  debugButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  centerPin: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -30 }],
    zIndex: 1000,
  },
  pinContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

});

export default LocationPermissionScreen;
