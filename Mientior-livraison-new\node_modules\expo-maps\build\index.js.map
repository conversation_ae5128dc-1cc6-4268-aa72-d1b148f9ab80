{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAEzD,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,KAAK,WAAW,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D;;GAEG;AACH,MAAM,KAAW,UAAU,CAkB1B;AAlBD,WAAiB,UAAU;IACZ,eAAI,GAAG,cAAc,CAAC;IACtB,qBAAU,GAAG,gBAAgB,CAAC;IAE9B,kBAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAGxC,yBAAc,GAAG,WAAW,CAAC,qBAAqB,CAAC;AAWlE,CAAC,EAlBgB,UAAU,KAAV,UAAU,QAkB1B;AAED;;GAEG;AACH,MAAM,KAAW,SAAS,CAYzB;AAZD,WAAiB,SAAS;IACX,cAAI,GAAG,aAAa,CAAC;IAErB,iBAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC;AASrD,CAAC,EAZgB,SAAS,KAAT,SAAS,QAYzB;AAED,MAAM,CAAC,MAAM,uBAAuB,GAAG,QAAQ,CAAC,uBAAuB,CAAC;AACxE,MAAM,CAAC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;AAEhE;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;IACzD,SAAS,EAAE,mBAAmB;IAC9B,aAAa,EAAE,uBAAuB;CACvC,CAAC,CAAC;AAEH,cAAc,gBAAgB,CAAC", "sourcesContent": ["import { create<PERSON><PERSON><PERSON>H<PERSON> } from 'expo-modules-core';\n\nimport ExpoMaps from './ExpoMaps';\nimport * as AppleTypes from './apple/AppleMaps.types';\nimport { AppleMapsView } from './apple/AppleMapsView';\nimport * as GoogleTypes from './google/GoogleMaps.types';\nimport { GoogleMapsView } from './google/GoogleMapsView';\nimport { GoogleStreetView } from './google/GoogleStreetView';\n\n/**\n * @hidden\n */\nexport namespace GoogleMaps {\n  export const View = GoogleMapsView;\n  export const StreetView = GoogleStreetView;\n\n  export const MapType = GoogleTypes.GoogleMapsMapType;\n  export type MapType = GoogleTypes.GoogleMapsMapType;\n\n  export const MapColorScheme = GoogleTypes.GoogleMapsColorScheme;\n  export type MapColorScheme = GoogleTypes.GoogleMapsColorScheme;\n\n  export type Marker = GoogleTypes.GoogleMapsMarker;\n  export type MapUISettings = GoogleTypes.GoogleMapsUISettings;\n  export type MapProperties = GoogleTypes.GoogleMapsProperties;\n\n  export type MapProps = GoogleTypes.GoogleMapsViewProps;\n  export type MapView = GoogleTypes.GoogleMapsViewType;\n\n  export type StreetViewProps = GoogleTypes.GoogleStreetViewProps;\n}\n\n/**\n * @hidden\n */\nexport namespace AppleMaps {\n  export const View = AppleMapsView;\n\n  export const MapType = AppleTypes.AppleMapsMapType;\n  export type MapType = AppleTypes.AppleMapsMapType;\n\n  export type Marker = AppleTypes.AppleMapsMarker;\n  export type MapUISettings = AppleTypes.AppleMapsUISettings;\n  export type MapProperties = AppleTypes.AppleMapsProperties;\n\n  export type MapProps = AppleTypes.AppleMapsViewProps;\n  export type MapView = AppleTypes.AppleMapsViewType;\n}\n\nexport const requestPermissionsAsync = ExpoMaps.requestPermissionsAsync;\nexport const getPermissionsAsync = ExpoMaps.getPermissionsAsync;\n\n/**\n * Check or request permissions to access the location.\n * This uses both `requestPermissionsAsync` and `getPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = useLocationPermissions();\n * ```\n */\nexport const useLocationPermissions = createPermissionHook({\n  getMethod: getPermissionsAsync,\n  requestMethod: requestPermissionsAsync,\n});\n\nexport * from './shared.types';\n"]}