#!/usr/bin/env node

/**
 * Vérification de la nouvelle clé API Google Maps
 */

const fs = require('fs');

const NEW_API_KEY = 'AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s';
const OLD_API_KEY = 'AIzaSyBgNfqhT6lk47eh3gA0Oc9uxsB16r5lTMY';

console.log('🔑 Vérification de la nouvelle clé API Google Maps');
console.log('================================================\n');

console.log('🆕 Nouvelle clé API:', NEW_API_KEY.substring(0, 15) + '...');
console.log('🗑️ Ancienne clé API:', OLD_API_KEY.substring(0, 15) + '...\n');

// Fichiers à vérifier
const filesToCheck = [
  { path: '.env', description: 'Variables d\'environnement' },
  { path: 'plugins/google-maps-config.js', description: 'Plugin Google Maps' }
];

console.log('📁 Vérification des fichiers:');

let allUpdated = true;

filesToCheck.forEach(file => {
  try {
    const content = fs.readFileSync(file.path, 'utf8');
    
    if (content.includes(NEW_API_KEY)) {
      console.log(`   ✅ ${file.path} - ${file.description}`);
    } else if (content.includes(OLD_API_KEY)) {
      console.log(`   ❌ ${file.path} - Contient encore l'ancienne clé`);
      allUpdated = false;
    } else {
      console.log(`   ⚠️ ${file.path} - Aucune clé API trouvée`);
    }
  } catch (error) {
    console.log(`   ❌ ${file.path} - Erreur de lecture: ${error.message}`);
    allUpdated = false;
  }
});

console.log('\n🧪 Test de la nouvelle clé API:');

// Test simple de la clé API
const https = require('https');

function testApiKey() {
  return new Promise((resolve, reject) => {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=Dakar&key=${NEW_API_KEY}`;
    
    https.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', reject);
  });
}

testApiKey()
  .then(response => {
    if (response.status === 'OK') {
      console.log('   ✅ Nouvelle clé API fonctionne parfaitement !');
      console.log(`   📍 ${response.results.length} résultat(s) trouvé(s)`);
      if (response.results.length > 0) {
        console.log(`   📍 Adresse: ${response.results[0].formatted_address}`);
      }
    } else {
      console.log(`   ❌ Erreur API: ${response.status}`);
      if (response.error_message) {
        console.log(`   💬 Message: ${response.error_message}`);
      }
    }
  })
  .catch(error => {
    console.log(`   ❌ Erreur de test: ${error.message}`);
  })
  .finally(() => {
    console.log('\n📊 Résumé:');
    if (allUpdated) {
      console.log('   ✅ Tous les fichiers ont été mis à jour');
      console.log('   ✅ Nouvelle clé API configurée');
      console.log('   ✅ Test API réussi');
    } else {
      console.log('   ⚠️ Certains fichiers n\'ont pas été mis à jour');
    }
    
    console.log('\n🚀 Prochaines étapes:');
    console.log('   1. Redémarrez l\'application: npx expo start --clear');
    console.log('   2. Testez l\'affichage de la carte');
    console.log('   3. Vérifiez les logs console');
    
    console.log('\n🔑 Clé API mise à jour avec succès !');
  });
