import React, { useState } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import MapView, { PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

export const SimpleMapTest: React.FC = () => {
  const [mapReady, setMapReady] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test Simple Google Maps</Text>
      
      <View style={styles.mapContainer}>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          initialRegion={{
            latitude: 14.6928, // Dakar
            longitude: -17.4467,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          onMapReady={() => {
            console.log('✅ Simple Map Ready');
            setMapReady(true);
          }}
          onMapLoaded={() => {
            console.log('✅ Simple Map Loaded');
            setMapLoaded(true);
          }}
        />
        
        {/* Overlay de statut */}
        <View style={styles.statusOverlay}>
          <Text style={styles.statusText}>
            Ready: {mapReady ? '✅' : '⏳'}
          </Text>
          <Text style={styles.statusText}>
            Loaded: {mapLoaded ? '✅' : '⏳'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  mapContainer: {
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  statusOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 10,
    borderRadius: 5,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    marginBottom: 2,
  },
});
