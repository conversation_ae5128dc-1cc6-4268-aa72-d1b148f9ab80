/**
 * The type of map to display.
 * @platform ios
 */
export var AppleMapsMapType;
(function (AppleMapsMapType) {
    /**
     * A satellite image of the area with road and road name layers on top.
     */
    AppleMapsMapType["HYBRID"] = "HYBRID";
    /**
     * A street map that shows the position of all roads and some road names.
     */
    AppleMapsMapType["STANDARD"] = "STANDARD";
    /**
     * A satellite image of the area.
     */
    AppleMapsMapType["IMAGERY"] = "IMAGERY";
})(AppleMapsMapType || (AppleMapsMapType = {}));
/**
 * The style of the polyline.
 * @platform ios
 */
export var AppleMapsContourStyle;
(function (AppleMapsContourStyle) {
    /**
     * A straight line.
     */
    AppleMapsContourStyle["STRAIGHT"] = "STRAIGHT";
    /**
     * A geodesic line.
     */
    AppleMapsContourStyle["GEODESIC"] = "GEODESIC";
})(AppleMapsContourStyle || (AppleMapsContourStyle = {}));
//# sourceMappingURL=AppleMaps.types.js.map