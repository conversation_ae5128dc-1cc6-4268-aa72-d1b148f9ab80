#!/usr/bin/env node

/**
 * Vérification complète de la configuration Google Maps
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification de la configuration Google Maps\n');

// 1. Vérifier le fichier .env
console.log('1️⃣ Fichier .env:');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const apiKeyMatch = envContent.match(/GOOGLE_MAPS_API_KEY=(.+)/);
  if (apiKeyMatch) {
    const apiKey = apiKeyMatch[1].trim();
    console.log('   ✅ Clé API trouvée:', apiKey.substring(0, 10) + '...');
  } else {
    console.log('   ❌ Clé API non trouvée');
  }
} catch (error) {
  console.log('   ❌ Erreur lecture .env:', error.message);
}

// 2. Vérifier app.config.js
console.log('\n2️⃣ Configuration app.config.js:');
try {
  const configPath = path.join(__dirname, 'app.config.js');
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  if (configContent.includes('googleMapsApiKey')) {
    console.log('   ✅ Configuration Google Maps présente');
  } else {
    console.log('   ❌ Configuration Google Maps manquante');
  }
  
  if (configContent.includes('process.env.GOOGLE_MAPS_API_KEY')) {
    console.log('   ✅ Variable d\'environnement référencée');
  } else {
    console.log('   ❌ Variable d\'environnement non référencée');
  }
} catch (error) {
  console.log('   ❌ Erreur lecture app.config.js:', error.message);
}

// 3. Vérifier le plugin Google Maps
console.log('\n3️⃣ Plugin Google Maps:');
try {
  const pluginPath = path.join(__dirname, 'plugins', 'google-maps-config.js');
  if (fs.existsSync(pluginPath)) {
    console.log('   ✅ Plugin google-maps-config.js présent');
    const pluginContent = fs.readFileSync(pluginPath, 'utf8');
    if (pluginContent.includes('AIzaSyBgNfqhT6lk47eh3gA0Oc9uxsB16r5lTMY')) {
      console.log('   ✅ Clé API hardcodée dans le plugin');
    }
  } else {
    console.log('   ❌ Plugin google-maps-config.js manquant');
  }
} catch (error) {
  console.log('   ❌ Erreur vérification plugin:', error.message);
}

// 4. Vérifier package.json pour react-native-maps
console.log('\n4️⃣ Dépendances:');
try {
  const packagePath = path.join(__dirname, 'package.json');
  const packageContent = fs.readFileSync(packagePath, 'utf8');
  const packageJson = JSON.parse(packageContent);
  
  if (packageJson.dependencies && packageJson.dependencies['react-native-maps']) {
    console.log('   ✅ react-native-maps installé:', packageJson.dependencies['react-native-maps']);
  } else {
    console.log('   ❌ react-native-maps manquant');
  }
  
  if (packageJson.dependencies && packageJson.dependencies['expo-location']) {
    console.log('   ✅ expo-location installé:', packageJson.dependencies['expo-location']);
  } else {
    console.log('   ❌ expo-location manquant');
  }
} catch (error) {
  console.log('   ❌ Erreur lecture package.json:', error.message);
}

console.log('\n📋 Résumé:');
console.log('- Si tout est ✅, la configuration est correcte');
console.log('- Si vous voyez des ❌, corrigez les problèmes identifiés');
console.log('- Redémarrez l\'app avec: npx expo start --clear');

console.log('\n🔧 Commandes utiles:');
console.log('- Test API: node quick-test-maps.js');
console.log('- Redémarrage: npx expo start --clear');
console.log('- Logs détaillés: npx expo start --clear --verbose');
