{"version": 3, "file": "GoogleMaps.types.js", "sourceRoot": "", "sources": ["../../src/google/GoogleMaps.types.ts"], "names": [], "mappings": "AA8NA;;;GAGG;AACH,MAAM,CAAN,IAAY,iBAiBX;AAjBD,WAAY,iBAAiB;IAC3B;;OAEG;IACH,sCAAiB,CAAA;IACjB;;OAEG;IACH,sCAAiB,CAAA;IACjB;;OAEG;IACH,4CAAuB,CAAA;IACvB;;OAEG;IACH,wCAAmB,CAAA;AACrB,CAAC,EAjBW,iBAAiB,KAAjB,iBAAiB,QAiB5B;AA+CD;;GAEG;AACH,MAAM,CAAN,IAAY,qBAIX;AAJD,WAAY,qBAAqB;IAC/B,wCAAe,CAAA;IACf,sCAAa,CAAA;IACb,wDAA+B,CAAA;AACjC,CAAC,EAJW,qBAAqB,KAArB,qBAAqB,QAIhC", "sourcesContent": ["import type { SharedRefType } from 'expo';\nimport type { Ref } from 'react';\nimport type { ProcessedColorValue, StyleProp, ViewStyle } from 'react-native';\n\nimport { CameraPosition, Coordinates } from '../shared.types';\n\n/**\n * @platform android\n */\nexport type GoogleMapsMarker = {\n  /**\n   * The unique identifier for the marker. This can be used to identify the clicked marker in the `onMarkerClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the marker.\n   */\n  coordinates?: Coordinates;\n\n  /**\n   * The title of the marker, displayed in the callout when the marker is clicked.\n   */\n  title?: string;\n\n  /**\n   * The snippet of the marker, Displayed in the callout when the marker is clicked.\n   */\n  snippet?: string;\n\n  /**\n   * Whether the marker is draggable.\n   */\n  draggable?: boolean;\n\n  /**\n   * Whether the callout should be shown when the marker is clicked.\n   */\n  showCallout?: boolean;\n\n  /**\n   * The custom icon to display for the marker.\n   */\n  icon?: SharedRefType<'image'>;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsPolyline = {\n  /**\n   * The unique identifier for the polyline. This can be used to identify the clicked polyline in the `onPolylineClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the polyline.\n   */\n  coordinates: Coordinates[];\n\n  /**\n   * The color of the polyline.\n   */\n  color?: ProcessedColorValue | string;\n\n  /**\n   * The width of the polyline.\n   */\n  width?: number;\n\n  /**\n   * Whether the polyline is geodesic.\n   */\n  geodesic?: boolean;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsCircle = {\n  /**\n   * The unique identifier for the circle. This can be used to identify the clicked circle in the `onCircleClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the circle.\n   */\n  center: Coordinates;\n\n  /**\n   * The radius of the circle.\n   */\n  radius: number;\n\n  /**\n   * The color of the circle.\n   */\n  color?: ProcessedColorValue | string;\n\n  /**\n   * The color of the circle line.\n   */\n  lineColor?: ProcessedColorValue | string;\n\n  /**\n   * The width of the circle line.\n   */\n  lineWidth?: number;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsPolygon = {\n  /**\n   * The unique identifier for the polygon. This can be used to identify the clicked polygon in the `onPolygonClick` event.\n   */\n  id?: string;\n\n  /**\n   * The coordinates of the circle.\n   */\n  coordinates: Coordinates[];\n\n  /**\n   * The color of the polygon.\n   */\n  color?: ProcessedColorValue | string;\n\n  /**\n   * The width of the polygon.\n   */\n  lineWidth?: number;\n\n  /**\n   * The color of the polygon.\n   */\n  lineColor?: ProcessedColorValue | string;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsUserLocation = {\n  /**\n   * User location coordinates.\n   */\n  coordinates: Coordinates;\n\n  /**\n   * Should the camera follow the users' location.\n   */\n  followUserLocation: boolean;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsUISettings = {\n  /**\n   * Whether the compass is enabled on the map.\n   * If enabled, the compass is only visible when the map is rotated.\n   */\n  compassEnabled?: boolean;\n\n  /**\n   * Whether the indoor level picker is enabled .\n   */\n  indoorLevelPickerEnabled?: boolean;\n\n  /**\n   * Whether the map toolbar is visible.\n   */\n  mapToolbarEnabled?: boolean;\n\n  /**\n   * Whether the my location button is visible.\n   */\n  myLocationButtonEnabled?: boolean;\n\n  /**\n   * Whether rotate gestures are enabled.\n   */\n  rotationGesturesEnabled?: boolean;\n\n  /**\n   * Whether the scroll gestures are enabled.\n   */\n  scrollGesturesEnabled?: boolean;\n\n  /**\n   * Whether the scroll gestures are enabled during rotation or zoom.\n   */\n  scrollGesturesEnabledDuringRotateOrZoom?: boolean;\n\n  /**\n   * Whether the tilt gestures are enabled.\n   */\n  tiltGesturesEnabled?: boolean;\n\n  /**\n   * Whether the zoom controls are visible.\n   */\n  zoomControlsEnabled?: boolean;\n\n  /**\n   * Whether the zoom gestures are enabled.\n   */\n  zoomGesturesEnabled?: boolean;\n\n  /**\n   * Whether the scale bar is displayed when zooming.\n   */\n  scaleBarEnabled?: boolean;\n\n  /**\n   * Whether the user is allowed to change the pitch type.\n   */\n  togglePitchEnabled?: boolean;\n};\n\n/**\n * The type of map to display.\n * @platform android\n */\nexport enum GoogleMapsMapType {\n  /**\n   * Satellite imagery with roads and points of interest overlayed.\n   */\n  HYBRID = 'HYBRID',\n  /**\n   * Standard road map.\n   */\n  NORMAL = 'NORMAL',\n  /**\n   * Satellite imagery.\n   */\n  SATELLITE = 'SATELLITE',\n  /**\n   * Topographic data.\n   */\n  TERRAIN = 'TERRAIN',\n}\n\n/**\n * @platform android\n */\nexport type GoogleMapsProperties = {\n  /**\n   * Whether the building layer is enabled on the map.\n   */\n  isBuildingEnabled?: boolean;\n\n  /**\n   * Whether the indoor layer is enabled on the map.\n   */\n  isIndoorEnabled?: boolean;\n\n  /**\n   * Whether finding the user's location is enabled on the map.\n   */\n  isMyLocationEnabled?: boolean;\n\n  /**\n   * Whether the traffic layer is enabled on the map.\n   */\n  isTrafficEnabled?: boolean;\n\n  /**\n   * Defines which map type should be used.\n   */\n  mapType?: GoogleMapsMapType;\n\n  /**\n   * If true, the user can select a location on the map to get more information.\n   */\n  selectionEnabled?: boolean;\n\n  /**\n   * The maximum zoom level for the map.\n   */\n  maxZoomPreference?: number;\n\n  /**\n   * The minimum zoom level for the map.\n   */\n  minZoomPreference?: number;\n};\n\n/**\n * @platform android\n */\nexport enum GoogleMapsColorScheme {\n  LIGHT = 'LIGHT',\n  DARK = 'DARK',\n  FOLLOW_SYSTEM = 'FOLLOW_SYSTEM',\n}\n\n/**\n * @platform android\n */\nexport type GoogleMapsViewProps = {\n  ref?: Ref<GoogleMapsViewType>;\n  style?: StyleProp<ViewStyle>;\n\n  /**\n   * The initial camera position of the map.\n   */\n  cameraPosition?: CameraPosition;\n\n  /**\n   * The array of markers to display on the map.\n   */\n  markers?: GoogleMapsMarker[];\n\n  /**\n   * The array of polylines to display on the map.\n   */\n  polylines?: GoogleMapsPolyline[];\n\n  /**\n   * The array of polygons to display on the map.\n   */\n  polygons?: GoogleMapsPolygon[];\n\n  /**\n   * The array of circles to display on the map.\n   */\n  circles?: GoogleMapsCircle[];\n\n  /**\n   * The `MapUiSettings` to be used for UI-specific settings on the map.\n   */\n  uiSettings?: GoogleMapsUISettings;\n\n  /**\n   * The properties for the map.\n   */\n  properties?: GoogleMapsProperties;\n\n  /**\n   * Defines the color scheme for the map.\n   */\n  colorScheme?: GoogleMapsColorScheme;\n\n  /**\n   * User location, overrides default behavior.\n   */\n  userLocation?: GoogleMapsUserLocation;\n\n  /**\n   * Lambda invoked when the map is loaded.\n   */\n  onMapLoaded?: () => void;\n\n  /**\n   * Lambda invoked when the user clicks on the map.\n   * It won't be invoked if the user clicks on POI or a marker.\n   */\n  onMapClick?: (event: { coordinates: Coordinates }) => void;\n\n  /**\n   * Lambda invoked when the user long presses on the map.\n   */\n  onMapLongClick?: (event: { coordinates: Coordinates }) => void;\n\n  /**\n   * Lambda invoked when a POI is clicked.\n   */\n  onPOIClick?: (event: { name: string; coordinates: Coordinates }) => void;\n\n  /**\n   * Lambda invoked when the marker is clicked\n   */\n  onMarkerClick?: (event: GoogleMapsMarker) => void;\n\n  /**\n   * Lambda invoked when the polyline is clicked.\n   */\n  onPolylineClick?: (event: GoogleMapsPolyline) => void;\n\n  /**\n   * Lambda invoked when the polygon is clicked.\n   */\n  onPolygonClick?: (event: GoogleMapsPolygon) => void;\n\n  /**\n   * Lambda invoked when the circle is clicked.\n   */\n  onCircleClick?: (event: GoogleMapsCircle) => void;\n\n  /**\n   * Lambda invoked when the map was moved by the user.\n   */\n  onCameraMove?: (event: {\n    coordinates: Coordinates;\n    zoom: number;\n    tilt: number;\n    bearing: number;\n  }) => void;\n};\n\n/**\n * @platform android\n */\nexport type SetCameraPositionConfig = CameraPosition & {\n  /**\n   * The duration of the animation in milliseconds.\n   */\n  duration?: number;\n};\n\n/**\n * @platform android\n */\nexport type GoogleMapsViewType = {\n  /**\n   * Update camera position.\n   * @param config New camera position config.\n   */\n  setCameraPosition: (config?: SetCameraPositionConfig) => void;\n};\n\n/**\n * @platform android\n */\nexport type StreetViewCameraPosition = {\n  coordinates: Coordinates;\n  zoom?: number;\n  tilt?: number;\n  bearing?: number;\n};\n\n/**\n * @platform android\n */\nexport type GoogleStreetViewProps = {\n  style?: StyleProp<ViewStyle>;\n  position: StreetViewCameraPosition;\n  isPanningGesturesEnabled?: boolean;\n  isStreetNamesEnabled?: boolean;\n  isUserNavigationEnabled?: boolean;\n  isZoomGesturesEnabled?: boolean;\n};\n"]}