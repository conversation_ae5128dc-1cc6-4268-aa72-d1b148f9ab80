#!/bin/bash

echo "🚀 Redémarrage de l'application avec carte forcée"
echo "================================================"

# Tuer tous les processus Expo existants
echo "🔄 Arrêt des processus Expo existants..."
pkill -f "expo start" 2>/dev/null || true
pkill -f "metro" 2>/dev/null || true

# Attendre un peu
sleep 2

# Test rapide de la clé API
echo "🔍 Test de la clé API Google Maps..."
node quick-test-maps.js

echo ""
echo "🚀 Démarrage de l'application..."
echo "📱 Ouvrez l'application et vérifiez si la carte s'affiche"
echo "🔧 Utilisez les boutons de debug si nécessaire"
echo ""

# Démarrer Expo avec cache clear
npx expo start --clear
