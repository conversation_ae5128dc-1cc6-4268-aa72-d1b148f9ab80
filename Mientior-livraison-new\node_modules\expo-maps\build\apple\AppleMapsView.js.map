{"version": 3, "file": "AppleMapsView.js", "sourceRoot": "", "sources": ["../../src/apple/AppleMapsView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAKtD,IAAI,UAA0D,CAAC;AAE/D,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;IAC1B,UAAU,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAI,WAA+B;IACxD,OAAO,KAAK,CAAC,WAAW;IACtB,sGAAsG;IACtG,CAAC,KAAU,EAAE,EAAE;QACb,WAAW,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,EACD,CAAC,WAAW,CAAC,CACd,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAC3C,CACE,EACE,UAAU,EACV,aAAa,EACb,YAAY,EACZ,eAAe,EACf,aAAa,EACb,cAAc,EACd,WAAW,EACX,SAAS,EACT,OAAO,EACP,QAAQ,EACR,GAAG,KAAK,EACT,EACD,GAAG,EACH,EAAE;IACF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAoB,IAAI,CAAC,CAAC;IACxD,KAAK,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACpC,iBAAiB,CAAC,MAAuB;YACvC,OAAO,SAAS,CAAC,OAAO,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC;QACD,KAAK,CAAC,mBAAmB,CAAC,WAAwB;YAChD,OAAO,SAAS,CAAC,OAAO,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7D,CAAC;KACF,CAAC,CAAC,CAAC;IAEJ,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IACpD,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAC1D,MAAM,kBAAkB,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;IACxD,MAAM,qBAAqB,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;IAC9D,MAAM,oBAAoB,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IAC5D,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAE1D,MAAM,eAAe,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpD,GAAG,QAAQ;QACX,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS;KACjD,CAAC,CAAC,CAAC;IAEJ,MAAM,cAAc,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,GAAG,OAAO;QACV,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS;QAC/C,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS;KACxD,CAAC,CAAC,CAAC;IAEJ,MAAM,aAAa,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC9C,GAAG,MAAM;QACT,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,SAAS;QAC9C,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,SAAS;KACvD,CAAC,CAAC,CAAC;IAEJ,MAAM,iBAAiB,GAAG,WAAW,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1D,GAAG,UAAU;QACb,mBAAmB;QACnB,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,yBAAyB;KACjD,CAAC,CAAC,CAAC;IAEJ,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,CAAC,UAAU,CACT,IAAI,KAAK,CAAC,CACV,GAAG,CAAC,CAAC,SAAS,CAAC,CACf,SAAS,CAAC,CAAC,eAAe,CAAC,CAC3B,QAAQ,CAAC,CAAC,cAAc,CAAC,CACzB,OAAO,CAAC,CAAC,aAAa,CAAC,CACvB,WAAW,CAAC,CAAC,iBAAiB,CAAC,CAC/B,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAC7B,aAAa,CAAC,CAAC,mBAAmB,CAAC,CACnC,YAAY,CAAC,CAAC,kBAAkB,CAAC,CACjC,eAAe,CAAC,CAAC,qBAAqB,CAAC,CACvC,cAAc,CAAC,CAAC,oBAAoB,CAAC,CACrC,aAAa,CAAC,CAAC,mBAAmB,CAAC,EACnC,CACH,CAAC;AACJ,CAAC,CACF,CAAC", "sourcesContent": ["import { requireNativeView } from 'expo';\nimport * as React from 'react';\nimport { Platform, processColor } from 'react-native';\n\nimport { CameraPosition, Coordinates } from '../shared.types';\nimport type { AppleMapsViewProps, AppleMapsViewType } from './AppleMaps.types';\n\nlet NativeView: React.ComponentType<AppleMapsViewProps> | null;\n\nif (Platform.OS === 'ios') {\n  NativeView = requireNativeView('ExpoAppleMaps');\n}\n\nfunction useNativeEvent<T>(userHandler?: (data: T) => void) {\n  return React.useCallback(\n    // TODO(@kitten): We unwrap a native payload here, but this isn't reflected in NativeView's prop types\n    (event: any) => {\n      userHandler?.(event.nativeEvent);\n    },\n    [userHandler]\n  );\n}\n\n/**\n * @platform ios\n */\nexport const AppleMapsView = React.forwardRef<AppleMapsViewType, AppleMapsViewProps>(\n  (\n    {\n      onMapClick,\n      onMarkerClick,\n      onCameraMove,\n      onPolylineClick,\n      onCircleClick,\n      onPolygonClick,\n      annotations,\n      polylines,\n      circles,\n      polygons,\n      ...props\n    },\n    ref\n  ) => {\n    const nativeRef = React.useRef<AppleMapsViewType>(null);\n    React.useImperativeHandle(ref, () => ({\n      setCameraPosition(config?: CameraPosition) {\n        return nativeRef.current?.setCameraPosition(config);\n      },\n      async openLookAroundAsync(coordinates: Coordinates) {\n        return nativeRef.current?.openLookAroundAsync(coordinates);\n      },\n    }));\n\n    const onNativeMapClick = useNativeEvent(onMapClick);\n    const onNativeMarkerClick = useNativeEvent(onMarkerClick);\n    const onNativeCameraMove = useNativeEvent(onCameraMove);\n    const onNativePolylineClick = useNativeEvent(onPolylineClick);\n    const onNativePolygonClick = useNativeEvent(onPolygonClick);\n    const onNativeCircleClick = useNativeEvent(onCircleClick);\n\n    const parsedPolylines = polylines?.map((polyline) => ({\n      ...polyline,\n      color: processColor(polyline.color) ?? undefined,\n    }));\n\n    const parsedPolygons = polygons?.map((polygon) => ({\n      ...polygon,\n      color: processColor(polygon.color) ?? undefined,\n      lineColor: processColor(polygon.lineColor) ?? undefined,\n    }));\n\n    const parsedCircles = circles?.map((circle) => ({\n      ...circle,\n      color: processColor(circle.color) ?? undefined,\n      lineColor: processColor(circle.lineColor) ?? undefined,\n    }));\n\n    const parsedAnnotations = annotations?.map((annotation) => ({\n      ...annotation,\n      // @ts-expect-error\n      icon: annotation.icon?.__expo_shared_object_id__,\n    }));\n\n    if (!NativeView) {\n      return null;\n    }\n\n    return (\n      <NativeView\n        {...props}\n        ref={nativeRef}\n        polylines={parsedPolylines}\n        polygons={parsedPolygons}\n        circles={parsedCircles}\n        annotations={parsedAnnotations}\n        onMapClick={onNativeMapClick}\n        onMarkerClick={onNativeMarkerClick}\n        onCameraMove={onNativeCameraMove}\n        onPolylineClick={onNativePolylineClick}\n        onPolygonClick={onNativePolygonClick}\n        onCircleClick={onNativeCircleClick}\n      />\n    );\n  }\n);\n"]}